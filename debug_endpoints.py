#!/usr/bin/env python3
"""
Debug script to test specific failing endpoints and understand the issues.
"""

import requests
import json
import traceback
from datetime import datetime, timedelta

BASE_URL = "http://localhost:8000"

def debug_endpoint(endpoint, description):
    """Debug a specific endpoint with detailed error information."""
    print(f"\n🔍 Debugging: {description}")
    print(f"   Endpoint: {endpoint}")
    
    try:
        response = requests.get(f"{BASE_URL}{endpoint}")
        print(f"   Status Code: {response.status_code}")
        print(f"   Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"   ✅ Success: {json.dumps(data, indent=2)[:200]}...")
            except json.JSONDecodeError:
                print(f"   📄 Non-JSON Response: {response.text[:200]}...")
        else:
            print(f"   ❌ Error Response: {response.text}")
            
            # Try to get more detailed error from FastAPI docs
            try:
                error_data = response.json()
                if 'detail' in error_data:
                    print(f"   🔍 Error Detail: {error_data['detail']}")
            except:
                pass
                
    except Exception as e:
        print(f"   💥 Exception: {str(e)}")
        print(f"   📋 Traceback: {traceback.format_exc()}")

def test_database_connection():
    """Test basic database connectivity."""
    print("🔌 Testing Database Connection...")
    
    # Test basic health check first
    debug_endpoint("/api/health/", "Basic Health Check")
    
    # Test database-specific endpoints
    debug_endpoint("/api/health/database", "Database Health Check")
    debug_endpoint("/api/health/data-sources", "Data Sources Check")

def test_backlog_endpoints():
    """Test backlog-related endpoints."""
    print("\n📋 Testing Backlog Endpoints...")
    
    # Test working backlog endpoint first
    debug_endpoint("/api/backlog/", "Basic Backlog Data")
    
    # Test failing endpoints
    debug_endpoint("/api/backlog/summary", "Backlog Summary")
    debug_endpoint("/api/backlog/overdue", "Overdue Orders")

def test_analyzer_directly():
    """Test the analyzer directly to see if it's a routing issue."""
    print("\n🧪 Testing Analyzer Directly...")
    
    try:
        # Import the analyzer
        from app.core.database import get_analyzer
        from datetime import datetime, timedelta
        
        # Get analyzer instance
        analyzer = get_analyzer()
        print("   ✅ Analyzer instance obtained")
        
        # Test get_backlog_data method
        date_debut = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
        date_fin = datetime.now().strftime('%Y-%m-%d')
        
        print(f"   📅 Testing with dates: {date_debut} to {date_fin}")
        
        backlog_data = analyzer.get_backlog_data(date_debut, date_fin)
        
        if backlog_data is not None:
            print(f"   ✅ Backlog data retrieved: {len(backlog_data)} rows")
            if not backlog_data.empty:
                print(f"   📊 Columns: {list(backlog_data.columns)}")
                print(f"   📋 Sample data: {backlog_data.head(2).to_dict('records')}")
            else:
                print("   ⚠️ Backlog data is empty")
        else:
            print("   ❌ Backlog data is None")
            
    except Exception as e:
        print(f"   💥 Error testing analyzer: {str(e)}")
        print(f"   📋 Traceback: {traceback.format_exc()}")

def main():
    """Main debugging function."""
    print("🐛 FastAPI Endpoint Debugging Tool")
    print("=" * 50)
    
    # Check if server is running
    try:
        response = requests.get(f"{BASE_URL}/api/health/")
        if response.status_code != 200:
            print("❌ Server is not responding properly")
            return
    except:
        print("❌ Cannot connect to server. Please start it first.")
        return
    
    print("✅ Server is responding")
    
    # Run debugging tests
    test_database_connection()
    test_backlog_endpoints()
    test_analyzer_directly()
    
    print("\n" + "=" * 50)
    print("🏁 Debugging completed")

if __name__ == "__main__":
    main()
