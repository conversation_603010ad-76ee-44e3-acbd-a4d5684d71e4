#!/usr/bin/env python3
"""
Comprehensive test suite for the FastAPI Production Tracking application.
This script tests all endpoints and provides detailed feedback on issues.
"""

import requests
import json
import sys
import time
from datetime import datetime, timedelta
from typing import Dict, Any, Optional

# Base URL for the API
BASE_URL = "http://localhost:8000"

class APITester:
    """Comprehensive API testing class."""
    
    def __init__(self, base_url: str = BASE_URL):
        self.base_url = base_url
        self.session = requests.Session()
        self.results = {
            'total_tests': 0,
            'passed_tests': 0,
            'failed_tests': 0,
            'errors': []
        }
    
    def test_endpoint(self, endpoint: str, description: str, 
                     params: Optional[Dict] = None, 
                     expected_status: int = 200,
                     check_success_field: bool = True) -> bool:
        """Test a single endpoint with comprehensive error handling."""
        self.results['total_tests'] += 1
        
        try:
            url = f"{self.base_url}{endpoint}"
            start_time = time.time()
            
            if params:
                response = self.session.get(url, params=params)
            else:
                response = self.session.get(url)
            
            response_time = time.time() - start_time
            
            # Check HTTP status
            if response.status_code != expected_status:
                error_msg = f"HTTP {response.status_code} (expected {expected_status})"
                print(f"❌ {description}: {error_msg}")
                self.results['errors'].append(f"{endpoint}: {error_msg}")
                self.results['failed_tests'] += 1
                return False
            
            # For JSON responses, check structure
            if response.headers.get('content-type', '').startswith('application/json'):
                try:
                    data = response.json()
                    
                    # Check success field if expected
                    if check_success_field and isinstance(data, dict):
                        if not data.get('success', True):
                            error_msg = f"API returned success=false: {data.get('message', 'No message')}"
                            print(f"❌ {description}: {error_msg}")
                            self.results['errors'].append(f"{endpoint}: {error_msg}")
                            self.results['failed_tests'] += 1
                            return False
                    
                    # Success
                    time_info = f" ({response_time:.3f}s)" if response_time > 0.5 else ""
                    print(f"✅ {description}: SUCCESS{time_info}")
                    self.results['passed_tests'] += 1
                    return True
                    
                except json.JSONDecodeError as e:
                    error_msg = f"Invalid JSON response: {str(e)}"
                    print(f"❌ {description}: {error_msg}")
                    self.results['errors'].append(f"{endpoint}: {error_msg}")
                    self.results['failed_tests'] += 1
                    return False
            else:
                # Non-JSON response (like HTML)
                if response.status_code == expected_status:
                    print(f"✅ {description}: SUCCESS (non-JSON response)")
                    self.results['passed_tests'] += 1
                    return True
                else:
                    error_msg = f"Unexpected content type: {response.headers.get('content-type')}"
                    print(f"❌ {description}: {error_msg}")
                    self.results['errors'].append(f"{endpoint}: {error_msg}")
                    self.results['failed_tests'] += 1
                    return False
                    
        except requests.exceptions.ConnectionError:
            error_msg = "Connection failed (is the server running?)"
            print(f"❌ {description}: {error_msg}")
            self.results['errors'].append(f"{endpoint}: {error_msg}")
            self.results['failed_tests'] += 1
            return False
        except Exception as e:
            error_msg = f"Unexpected error: {str(e)}"
            print(f"❌ {description}: {error_msg}")
            self.results['errors'].append(f"{endpoint}: {error_msg}")
            self.results['failed_tests'] += 1
            return False
    
    def check_server_status(self) -> bool:
        """Check if the server is running and responding."""
        try:
            response = self.session.get(f"{self.base_url}/api/health/")
            return response.status_code == 200
        except:
            return False
    
    def run_all_tests(self):
        """Run the complete test suite."""
        print("🚀 Comprehensive FastAPI Production Tracking Test Suite")
        print("=" * 70)
        
        # Check server status
        if not self.check_server_status():
            print("❌ Server is not responding. Please start the FastAPI server first.")
            print("   Run: python main.py or python -m uvicorn main:app --reload")
            return False
        
        print("✅ Server is running and responding")
        print()
        
        # Date parameters for testing
        date_debut = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
        date_fin = datetime.now().strftime('%Y-%m-%d')
        
        # Test Health Routes
        print("🏥 Testing Health Routes...")
        self.test_endpoint("/api/health/", "Basic Health Check")
        self.test_endpoint("/api/health/detailed", "Detailed Health Check")
        self.test_endpoint("/api/health/database", "Database Health Check")
        self.test_endpoint("/api/health/data-sources", "Data Sources Check")
        self.test_endpoint("/api/health/performance", "Performance Check")
        self.test_endpoint("/api/health/version", "Version Info")
        print()
        
        # Test OF Routes
        print("📋 Testing OF Routes...")
        self.test_endpoint("/api/of/en_cours", "OF En Cours")
        self.test_endpoint("/api/of/histo", "OF Historique")
        self.test_endpoint("/api/of/all", "OF All Data")
        self.test_endpoint("/api/of/filtered", "OF Filtered", {"statut_filter": "C"})
        self.test_endpoint("/api/of/by_status/C", "OF By Status (C)")
        print()
        
        # Test Dashboard Routes
        print("📊 Testing Dashboard Routes...")
        self.test_endpoint("/", "Main Dashboard Page", check_success_field=False)
        self.test_endpoint("/api/dashboard-data", "Dashboard Data", 
                          {"date_debut": date_debut, "date_fin": date_fin})
        self.test_endpoint("/api/kpis", "KPIs")
        self.test_endpoint("/api/summary-stats", "Summary Stats")
        self.test_endpoint("/api/filters/options", "Filter Options")
        print()
        
        # Test Charge Routes
        print("⚙️ Testing Charge Routes...")
        self.test_endpoint("/api/charge/", "Charge Data")
        self.test_endpoint("/api/charge/by_sector", "Charge By Sector")
        self.test_endpoint("/api/charge/capacity", "Capacity Analysis")
        self.test_endpoint("/api/charge/overload", "Overload Analysis")
        self.test_endpoint("/api/charge/efficiency", "Efficiency Analysis")
        self.test_endpoint("/api/charge/planning", "Planning Data")
        print()
        
        # Test Backlog Routes
        print("📋 Testing Backlog Routes...")
        self.test_endpoint("/api/backlog/", "Backlog Data")
        self.test_endpoint("/api/backlog/urgent", "Urgent Backlog")
        self.test_endpoint("/api/backlog/by_priority/URGENT", "Backlog By Priority")
        self.test_endpoint("/api/backlog/summary", "Backlog Summary")
        self.test_endpoint("/api/backlog/overdue", "Overdue Orders")
        print()
        
        # Test Personnel Routes
        print("👥 Testing Personnel Routes...")
        self.test_endpoint("/api/personnel/", "Personnel Data")
        self.test_endpoint("/api/personnel/by_sector/CMS", "Personnel By Sector")
        self.test_endpoint("/api/personnel/qualifications", "Qualifications")
        self.test_endpoint("/api/personnel/efficiency", "Personnel Efficiency")
        self.test_endpoint("/api/personnel/summary", "Personnel Summary")
        self.test_endpoint("/api/personnel/search", "Personnel Search", {"secteur": "CMS"})
        print()
        
        # Test Export Routes
        print("📤 Testing Export Routes...")
        self.test_endpoint("/api/export/formats", "Export Formats")
        print()
        
        # Print summary
        self.print_summary()
        
        return self.results['failed_tests'] == 0
    
    def print_summary(self):
        """Print test results summary."""
        print("=" * 70)
        print(f"📊 Test Results Summary:")
        print(f"   Total Tests: {self.results['total_tests']}")
        print(f"   ✅ Passed: {self.results['passed_tests']}")
        print(f"   ❌ Failed: {self.results['failed_tests']}")
        
        if self.results['failed_tests'] > 0:
            print(f"\n🔍 Failed Tests Details:")
            for error in self.results['errors']:
                print(f"   • {error}")
        
        success_rate = (self.results['passed_tests'] / self.results['total_tests']) * 100
        print(f"\n📈 Success Rate: {success_rate:.1f}%")
        
        if self.results['failed_tests'] == 0:
            print("🎉 All tests passed! The API is working correctly.")
        else:
            print("⚠️ Some tests failed. Check the server logs for more details.")
        
        print(f"\n💡 Access the application:")
        print(f"   🌐 Dashboard: {self.base_url}")
        print(f"   📚 API Docs: {self.base_url}/docs")
        print(f"   📖 ReDoc: {self.base_url}/redoc")


def main():
    """Main function to run the test suite."""
    tester = APITester()
    success = tester.run_all_tests()
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
