#!/usr/bin/env python3
"""
Test script for the FastAPI Production Tracking application.
"""

import requests
import json
from datetime import datetime, timed<PERSON><PERSON>

def test_api_endpoints():
    """Test all API endpoints to ensure they're working."""
    base_url = "http://localhost:8000"
    
    print("🧪 Testing FastAPI Production Tracking Application")
    print("=" * 60)
    
    # Test health check
    print("1. Testing Health Check...")
    try:
        response = requests.get(f"{base_url}/api/health")
        if response.status_code == 200:
            data = response.json()
            # Handle the SuccessResponse format
            if data.get('success') and data.get('data'):
                health_data = data['data']
                print(f"   ✅ Health Check: {health_data.get('status', 'unknown')}")
                print(f"   📊 Database Connected: {health_data.get('database_connected', False)}")
                print(f"   🔢 Version: {health_data.get('version', 'unknown')}")
            else:
                print(f"   ⚠️ Health Check: Unexpected response format")
                print(f"   📄 Response: {data}")
        else:
            print(f"   ❌ Health Check Failed: {response.status_code}")
    except requests.exceptions.ConnectionError:
        print("   ❌ Cannot connect to FastAPI server. Is it running?")
        print("   💡 Run: python run_fastapi.py")
        return False
    except Exception as e:
        print(f"   ❌ Health Check Error: {e}")
        return False
    
    # Test dashboard data
    print("\n2. Testing Dashboard Data...")
    try:
        response = requests.get(f"{base_url}/api/dashboard-data")
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                kpis = data.get('kpis', {})
                print(f"   ✅ Dashboard Data Retrieved")
                print(f"   📊 Total OF: {kpis.get('total_of', 0)}")
                print(f"   📊 OF en Cours: {kpis.get('of_en_cours', 0)}")
                print(f"   📊 Alertes: {kpis.get('alertes', 0)}")
            else:
                print("   ⚠️ Dashboard Data Retrieved but not successful")
        else:
            print(f"   ❌ Dashboard Data Failed: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Dashboard Data Error: {e}")
    
    # Test OF data with filters
    print("\n3. Testing OF Data with Filters...")
    try:
        params = {
            'date_debut': (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d'),
            'date_fin': datetime.now().strftime('%Y-%m-%d'),
            'statut_filter': 'C'
        }
        response = requests.get(f"{base_url}/api/of-data", params=params)
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                count = data.get('count', 0)
                print(f"   ✅ OF Data Retrieved: {count} records")
            else:
                print("   ⚠️ OF Data Retrieved but not successful")
        else:
            print(f"   ❌ OF Data Failed: {response.status_code}")
    except Exception as e:
        print(f"   ❌ OF Data Error: {e}")
    
    # Test filter options
    print("\n4. Testing Filter Options...")
    try:
        response = requests.get(f"{base_url}/api/filters/options")
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                options = data.get('options', {})
                print(f"   ✅ Filter Options Retrieved")
                print(f"   📊 Familles: {len(options.get('familles', []))}")
                print(f"   📊 Clients: {len(options.get('clients', []))}")
            else:
                print("   ⚠️ Filter Options Retrieved but not successful")
        else:
            print(f"   ❌ Filter Options Failed: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Filter Options Error: {e}")
    
    # Test report generation
    print("\n5. Testing Report Generation...")
    try:
        response = requests.get(f"{base_url}/api/report")
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                report = data.get('report', '')
                print(f"   ✅ Report Generated: {len(report)} characters")
            else:
                print("   ⚠️ Report Generated but not successful")
        else:
            print(f"   ❌ Report Generation Failed: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Report Generation Error: {e}")
    
    # Test summary stats
    print("\n6. Testing Summary Statistics...")
    try:
        response = requests.get(f"{base_url}/api/stats/summary")
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                summary = data.get('summary', {})
                print(f"   ✅ Summary Stats Retrieved")
                print(f"   📊 OF Stats: {summary.get('of_stats', {})}")
            else:
                print("   ⚠️ Summary Stats Retrieved but not successful")
        else:
            print(f"   ❌ Summary Stats Failed: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Summary Stats Error: {e}")
    
    print("\n" + "=" * 60)
    print("🎉 API Testing Complete!")
    print("💡 Access the dashboard at: http://localhost:8000")
    print("📚 API Documentation at: http://localhost:8000/docs")
    
    return True

def test_frontend():
    """Test if the frontend is accessible."""
    base_url = "http://localhost:8000"
    
    print("\n🌐 Testing Frontend Access...")
    try:
        response = requests.get(base_url)
        if response.status_code == 200:
            print("   ✅ Frontend accessible")
            print("   🎨 Dashboard HTML loaded successfully")
        else:
            print(f"   ❌ Frontend access failed: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Frontend access error: {e}")

if __name__ == "__main__":
    print("🚀 FastAPI Production Tracking - Test Suite")
    print("Make sure the FastAPI server is running before running this test.")
    print("Run: python run_fastapi.py")
    print()
    
    input("Press Enter to start testing (or Ctrl+C to cancel)...")
    
    # Test API endpoints
    api_success = test_api_endpoints()
    
    # Test frontend
    if api_success:
        test_frontend()
    
    print("\n✅ Testing completed!")
